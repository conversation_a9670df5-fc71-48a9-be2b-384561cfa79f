/* 抖音风格+紫色主色调 */
:root {
  --main-purple: #9F5FFF;
  --main-purple-dark: #7B2FF2;
  --main-purple-light: #A259FF;
  --bg-black: #181824;
  --text-white: #fff;
  --card-bg: #231942;
  --card-shadow: 0 4px 24px 0 rgba(127, 63, 255, 0.12);
}

body, html, #root {
  height: 100%;
  margin: 0;
  background: var(--bg-black);
  color: var(--text-white);
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.app-bg {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--main-purple-dark) 0%, var(--bg-black) 100%);
}

.header {
  padding: 40px 0 16px 0;
  text-align: center;
}
.logo {
  font-size: 2.2rem;
  font-weight: bold;
  letter-spacing: 2px;
  color: var(--main-purple);
  margin-bottom: 12px;
}
.slogan {
  font-size: 1.2rem;
  color: var(--main-purple-light);
  margin-bottom: 24px;
}
.section-title {
  font-size: 1.5rem;
  color: var(--main-purple);
  margin: 32px 0 16px 0;
  text-align: left;
  padding-left: 16px;
}
.main-content {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 12px 32px 12px;
}
.product-masonry {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  justify-content: flex-start;
}
.product-card {
  background: var(--card-bg);
  border-radius: 18px;
  box-shadow: var(--card-shadow);
  padding: 28px 20px 20px 20px;
  width: 220px;
  transition: transform 0.2s cubic-bezier(.4,2,.6,1), box-shadow 0.2s;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  margin-bottom: 12px;
}
.product-card:hover {
  transform: translateY(-10px) scale(1.04) rotate(-2deg);
  box-shadow: 0 8px 32px 0 rgba(127, 63, 255, 0.24);
}
.product-icon {
  font-size: 2.3rem;
  margin-bottom: 16px;
  color: var(--main-purple-light);
}
.product-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--main-purple);
}
.product-desc {
  font-size: 0.98rem;
  color: var(--text-white);
  opacity: 0.85;
}
.footer {
  text-align: center;
  color: #aaa;
  padding: 36px 0 18px 0;
  font-size: 0.95rem;
  letter-spacing: 1px;
}
@media (max-width: 600px) {
  .product-masonry {
    flex-direction: column;
    gap: 14px;
  }
  .product-card {
    width: 100%;
    padding: 22px 12px 16px 12px;
  }
  .main-content {
    padding: 0 4px 24px 4px;
  }
}
