/* 宠物社区页面样式 */
:root {
  --primary-color: #ff4d6a;
  --secondary-color: #ff8fa3;
  --background-color: #f8f8f8;
  --card-background: #ffffff;
  --text-primary: #333333;
  --text-secondary: #666666;
  --border-radius: 12px;
  --shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.pet-community {
  background-color: var(--background-color);
  min-height: 100vh;
  padding: 20px;
}

.masonry-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.post-card {
  background: var(--card-background);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.post-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.post-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  aspect-ratio: 1;
}

.post-content {
  padding: 15px;
}

.post-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.post-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 12px;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

.post-stats {
  display: flex;
  gap: 15px;
  margin-left: auto;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-secondary);
  font-size: 14px;
}

.stat-icon {
  font-size: 16px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .masonry-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    padding: 15px;
  }

  .post-title {
    font-size: 16px;
  }

  .post-description {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .masonry-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 12px;
  }

  .post-content {
    padding: 12px;
  }
}