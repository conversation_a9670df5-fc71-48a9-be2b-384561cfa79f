import React from 'react';
import styled, { createGlobalStyle } from 'styled-components';
import Minesweeper from './components/Minesweeper';
import GooseGame from './components/GooseGame';

const GlobalStyle = createGlobalStyle`
  html, body, #root {
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    background: #f8f8f8;
    font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  }
`;

const AppWrapper = styled.div`
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  display: flex;
  justify-content: center;
  align-items: center;
`;

function App() {
  return (
    <AppWrapper>
      <GlobalStyle />
      {/* <Minesweeper /> */}
      <GooseGame />
    </AppWrapper>
  );
}

export default App;
