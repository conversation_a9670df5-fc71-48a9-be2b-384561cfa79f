import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Fa<PERSON><PERSON>t, FaRegHeart, FaBookmark, FaRegBookmark, FaComment } from 'react-icons/fa';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const Masonry = styled.div`
  column-count: 2;
  column-gap: 16px;
  
  @media (min-width: 768px) {
    column-count: 3;
  }
  
  @media (min-width: 1024px) {
    column-count: 4;
  }
`;

const Card = styled.div`
  break-inside: avoid;
  margin-bottom: 16px;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
`;

const Image = styled.img`
  width: 100%;
  height: auto;
  object-fit: cover;
`;

const Content = styled.div`
  padding: 12px;
`;

const Title = styled.h3`
  margin: 0 0 8px;
  font-size: 16px;
  color: #333;
`;

const Description = styled.p`
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
`;

const Actions = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  border-top: 1px solid #f0f0f0;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  padding: 4px;
  transition: color 0.2s;

  &:hover {
    color: #ff4d6a;
  }

  &.active {
    color: #ff4d6a;
  }
`;

const CommentSection = styled.div`
  padding: 12px;
  border-top: 1px solid #f0f0f0;
`;

const CommentInput = styled.input`
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 8px;
`;

const CommentList = styled.div`
  max-height: 200px;
  overflow-y: auto;
`;

const Comment = styled.div`
  padding: 8px 0;
  font-size: 14px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  padding: 12px;
  border-top: 1px solid #f0f0f0;
`;

const Avatar = styled.img`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
`;

const Username = styled.span`
  font-size: 14px;
  color: #333;
`;

// 模拟数据
const mockPosts = [
  {
    id: 1,
    imageUrl: 'https://images.unsplash.com/photo-1543466835-00a7907e9de1',
    title: '超可爱的金毛宝宝',
    description: '今天带金毛宝宝去公园玩，太开心了！',
    user: {
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde',
      name: '宠物达人'
    }
  },
  {
    id: 2,
    imageUrl: 'https://images.unsplash.com/photo-1573865526739-10659fec78a5',
    title: '英短蓝猫的日常',
    description: '今天又是慵懒的一天，主子在沙发上睡得真香~',
    user: {
      avatar: 'https://images.unsplash.com/photo-1527980965255-d3b416303d12',
      name: '猫咪铲屎官'
    }
  },
  // 可以添加更多模拟数据
];

const PetCommunity = () => {
  const [posts, setPosts] = useState(mockPosts);
  const [activeComments, setActiveComments] = useState({});
  const [commentInputs, setCommentInputs] = useState({});
  const mockUserId = '123'; // 模拟当前用户ID

  useEffect(() => {
    // 这里可以添加获取真实数据的逻辑
  }, []);

  const handleLike = async (postId) => {
    try {
      const response = await fetch(`http://localhost:4000/api/posts/${postId}/like`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: mockUserId })
      });
      const updatedPost = await response.json();
      setPosts(posts.map(post => post.id === postId ? { ...post, likes: updatedPost.likes } : post));
    } catch (error) {
      console.error('Error liking post:', error);
    }
  };

  const handleFavorite = async (postId) => {
    try {
      const response = await fetch(`http://localhost:4000/api/posts/${postId}/favorite`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: mockUserId })
      });
      const updatedPost = await response.json();
      setPosts(posts.map(post => post.id === postId ? { ...post, favorites: updatedPost.favorites } : post));
    } catch (error) {
      console.error('Error favoriting post:', error);
    }
  };

  const handleComment = async (postId) => {
    if (!commentInputs[postId]) return;

    try {
      const response = await fetch(`http://localhost:4000/api/posts/${postId}/comment`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: commentInputs[postId],
          user: mockUserId
        })
      });
      const updatedPost = await response.json();
      setPosts(posts.map(post => post.id === postId ? { ...post, comments: updatedPost.comments } : post));
      setCommentInputs({ ...commentInputs, [postId]: '' });
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  const toggleComments = async (postId) => {
    if (!activeComments[postId]) {
      try {
        const response = await fetch(`http://localhost:4000/api/posts/${postId}/comments`);
        const comments = await response.json();
        setActiveComments({ ...activeComments, [postId]: comments });
      } catch (error) {
        console.error('Error fetching comments:', error);
      }
    } else {
      const newActiveComments = { ...activeComments };
      delete newActiveComments[postId];
      setActiveComments(newActiveComments);
    }
  };

  return (
    <Container>
      <Masonry>
        {posts.map(post => (
          <Card key={post.id}>
            <Image src={post.imageUrl} alt={post.title} loading="lazy" />
            <Content>
              <Title>{post.title}</Title>
              <Description>{post.description}</Description>
            </Content>
            <Actions>
              <ActionButton
                onClick={() => handleLike(post.id)}
                className={post.likes?.includes(mockUserId) ? 'active' : ''}
              >
                {post.likes?.includes(mockUserId) ? <FaHeart /> : <FaRegHeart />}
                {post.likes?.length || 0}
              </ActionButton>
              <ActionButton
                onClick={() => handleFavorite(post.id)}
                className={post.favorites?.includes(mockUserId) ? 'active' : ''}
              >
                {post.favorites?.includes(mockUserId) ? <FaBookmark /> : <FaRegBookmark />}
                {post.favorites?.length || 0}
              </ActionButton>
              <ActionButton onClick={() => toggleComments(post.id)}>
                <FaComment />
                {post.comments?.length || 0}
              </ActionButton>
            </Actions>
            {activeComments[post.id] && (
              <CommentSection>
                <CommentInput
                  type="text"
                  placeholder="添加评论..."
                  value={commentInputs[post.id] || ''}
                  onChange={(e) => setCommentInputs({ ...commentInputs, [post.id]: e.target.value })}
                  onKeyPress={(e) => e.key === 'Enter' && handleComment(post.id)}
                />
                <CommentList>
                  {activeComments[post.id].map((comment, index) => (
                    <Comment key={index}>
                      <strong>{comment.user}</strong>: {comment.content}
                    </Comment>
                  ))}
                </CommentList>
              </CommentSection>
            )}
            <UserInfo>
              <Avatar src={post.user.avatar} alt={post.user.name} />
              <Username>{post.user.name}</Username>
            </UserInfo>
          </Card>
        ))}
      </Masonry>
    </Container>
  );
};

export default PetCommunity;