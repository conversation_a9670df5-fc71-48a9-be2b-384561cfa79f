import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';

const GameWrapper = styled.div`
  width: 400px;
  height: 400px;
  background: #e0f7fa;
  border-radius: 20px;
  position: relative;
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const Goose = styled.div`
  position: absolute;
  font-size: 48px;
  cursor: pointer;
  user-select: none;
  transition: top 0.2s, left 0.2s;
`;

const ScoreBoard = styled.div`
  margin-bottom: 10px;
  font-size: 20px;
  font-weight: bold;
`;

const Timer = styled.div`
  margin-bottom: 10px;
  font-size: 18px;
`;

const RestartButton = styled.button`
  margin-top: 20px;
  padding: 8px 20px;
  font-size: 16px;
  border: none;
  border-radius: 8px;
  background: #4caf50;
  color: #fff;
  cursor: pointer;
  transition: background 0.2s;
  &:hover {
    background: #388e3c;
  }
`;

const GAME_TIME = 30; // seconds

function GooseGame() {
  const [score, setScore] = useState(0);
  const [timer, setTimer] = useState(GAME_TIME);
  const [isRunning, setIsRunning] = useState(true);
  const [goosePos, setGoosePos] = useState({ top: 100, left: 100 });
  const gameRef = useRef();

  // 倒计时
  useEffect(() => {
    if (!isRunning) return;
    if (timer === 0) {
      setIsRunning(false);
      return;
    }
    const interval = setInterval(() => {
      setTimer(t => t - 1);
    }, 1000);
    return () => clearInterval(interval);
  }, [timer, isRunning]);

  // 大鹅自动移动
  useEffect(() => {
    if (!isRunning) return;
    const moveGoose = () => {
      const box = gameRef.current.getBoundingClientRect();
      const gooseSize = 48;
      const maxTop = box.height - gooseSize;
      const maxLeft = box.width - gooseSize;
      const top = Math.random() * maxTop;
      const left = Math.random() * maxLeft;
      setGoosePos({ top, left });
    };
    const interval = setInterval(moveGoose, 800);
    return () => clearInterval(interval);
  }, [isRunning]);

  // 点击大鹅得分并立即移动
  const handleGooseClick = () => {
    if (!isRunning) return;
    setScore(s => s + 1);
    const box = gameRef.current.getBoundingClientRect();
    const gooseSize = 48;
    const maxTop = box.height - gooseSize;
    const maxLeft = box.width - gooseSize;
    const top = Math.random() * maxTop;
    const left = Math.random() * maxLeft;
    setGoosePos({ top, left });
  };

  const handleRestart = () => {
    setScore(0);
    setTimer(GAME_TIME);
    setIsRunning(true);
    setGoosePos({ top: 100, left: 100 });
  };

  return (
    <GameWrapper ref={gameRef}>
      <ScoreBoard>得分：{score}</ScoreBoard>
      <Timer>剩余时间：{timer} 秒</Timer>
      {isRunning ? (
        <Goose
          style={{ top: goosePos.top, left: goosePos.left }}
          onClick={handleGooseClick}
        >
          🦢
        </Goose>
      ) : (
        <>
          <div style={{ fontSize: '24px', margin: '20px 0' }}>游戏结束！你的得分：{score}</div>
          <RestartButton onClick={handleRestart}>再来一次</RestartButton>
        </>
      )}
    </GameWrapper>
  );
}

export default GooseGame; 