import React from 'react';
import PropTypes from 'prop-types';
import styled from 'styled-components';

const CardWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 90vh;
  width: 100vw;
  background: #111;
  color: #fff;
  position: relative;
  box-sizing: border-box;
`;

const Media = styled.div`
  width: 100vw;
  max-width: 420px;
  height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #222;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 24px;
`;

const Title = styled.h2`
  font-size: 2rem;
  margin: 0 0 12px 0;
`;

const Description = styled.p`
  font-size: 1.1rem;
  margin: 0;
  color: #ccc;
`;

const ProductCard = ({ title, description, mediaUrl, mediaType }) => (
  <CardWrapper>
    <Media>
      {mediaType === 'video' ? (
        <video
          src={mediaUrl}
          autoPlay
          loop
          muted
          playsInline
          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
        />
      ) : (
        <img
          src={mediaUrl}
          alt={title}
          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
        />
      )}
    </Media>
    <Title>{title}</Title>
    <Description>{description}</Description>
  </CardWrapper>
);

ProductCard.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  mediaUrl: PropTypes.string.isRequired,
  mediaType: PropTypes.oneOf(['video', 'image']).isRequired,
};

export default ProductCard;
