import React, { useState, useCallback } from 'react';
import styled from 'styled-components';

const GameContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
`;

const Board = styled.div`
  display: grid;
  grid-template-columns: repeat(15, 40px);
  grid-template-rows: repeat(15, 40px);
  background-color: #DEB887;
  gap: 1px;
  border: 2px solid #8B4513;
`;

const Cell = styled.div`
  width: 40px;
  height: 40px;
  background-color: #DEB887;
  position: relative;
  cursor: pointer;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    background-color: ${props => props.value === 'black' ? '#000' : props.value === 'white' ? '#fff' : 'transparent'};
  }

  &:hover::before {
    background-color: ${props => props.value ? 'transparent' : 'rgba(0, 0, 0, 0.1)'};
  }
`;

const Status = styled.div`
  margin: 20px 0;
  font-size: 24px;
  font-weight: bold;
`;

const Button = styled.button`
  padding: 10px 20px;
  font-size: 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    background-color: #45a049;
  }
`;

const Gomoku = () => {
  const [board, setBoard] = useState(Array(15).fill(null).map(() => Array(15).fill(null)));
  const [isBlackNext, setIsBlackNext] = useState(true);
  const [winner, setWinner] = useState(null);

  const checkWinner = useCallback((boardState, row, col) => {
    const directions = [
      [1, 0],   // horizontal
      [0, 1],   // vertical
      [1, 1],   // diagonal
      [1, -1],  // anti-diagonal
    ];

    const currentPlayer = boardState[row][col];

    for (const [dx, dy] of directions) {
      let count = 1;
      
      // Check forward
      for (let i = 1; i < 5; i++) {
        const newRow = row + dx * i;
        const newCol = col + dy * i;
        if (
          newRow < 0 || newRow >= 15 ||
          newCol < 0 || newCol >= 15 ||
          boardState[newRow][newCol] !== currentPlayer
        ) break;
        count++;
      }
      
      // Check backward
      for (let i = 1; i < 5; i++) {
        const newRow = row - dx * i;
        const newCol = col - dy * i;
        if (
          newRow < 0 || newRow >= 15 ||
          newCol < 0 || newCol >= 15 ||
          boardState[newRow][newCol] !== currentPlayer
        ) break;
        count++;
      }

      if (count >= 5) return currentPlayer;
    }
    return null;
  }, []);

  const handleClick = (row, col) => {
    if (winner || board[row][col]) return;

    const newBoard = board.map(row => [...row]);
    newBoard[row][col] = isBlackNext ? 'black' : 'white';
    setBoard(newBoard);

    const newWinner = checkWinner(newBoard, row, col);
    if (newWinner) {
      setWinner(newWinner);
    } else {
      setIsBlackNext(!isBlackNext);
    }
  };

  const resetGame = () => {
    setBoard(Array(15).fill(null).map(() => Array(15).fill(null)));
    setIsBlackNext(true);
    setWinner(null);
  };

  return (
    <GameContainer>
      <Status>
        {winner
          ? `获胜者: ${winner === 'black' ? '黑棋' : '白棋'}`
          : `下一步: ${isBlackNext ? '黑棋' : '白棋'}`
        }
      </Status>
      <Board>
        {board.map((row, rowIndex) => (
          row.map((cell, colIndex) => (
            <Cell
              key={`${rowIndex}-${colIndex}`}
              value={cell}
              onClick={() => handleClick(rowIndex, colIndex)}
            />
          ))
        ))}
      </Board>
      <Button onClick={resetGame}>重新开始</Button>
    </GameContainer>
  );
};

export default Gomoku;