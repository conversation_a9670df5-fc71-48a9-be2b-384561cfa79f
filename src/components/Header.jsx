import React from 'react';
import styled from 'styled-components';

const HeaderBar = styled.header`
  width: 100vw;
  height: 56px;
  background: rgba(20, 20, 20, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
`;

const Logo = styled.div`
  font-size: 1.7rem;
  font-weight: bold;
  color: #fff;
  letter-spacing: 2px;
  display: flex;
  align-items: center;
`;

const LogoIcon = styled.span`
  font-size: 2rem;
  margin-right: 10px;
`;

const Header = () => (
  <HeaderBar>
    <Logo>
      <LogoIcon>☁️</LogoIcon>
      云产品官方站
    </Logo>
  </HeaderBar>
);

export default Header; 