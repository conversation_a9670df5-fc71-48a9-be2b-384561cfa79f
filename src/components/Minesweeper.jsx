import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

const GameContainer = styled.div`
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
`;

const GameBoard = styled.div`
  display: grid;
  grid-template-columns: repeat(${props => props.size}, 30px);
  gap: 2px;
  background: #ccc;
  padding: 10px;
  border-radius: 4px;
`;

const Cell = styled.button`
  width: 30px;
  height: 30px;
  border: none;
  background: ${props => props.revealed ? '#fff' : '#e0e0e0'};
  cursor: pointer;
  font-weight: bold;
  color: ${props => {
    if (props.value === 1) return '#0000FF';
    if (props.value === 2) return '#008000';
    if (props.value === 3) return '#FF0000';
    if (props.value === 4) return '#000080';
    if (props.value === 5) return '#800000';
    if (props.value === 6) return '#008080';
    if (props.value === 7) return '#000000';
    if (props.value === 8) return '#808080';
    return '#000';
  }};
  &:hover {
    background: ${props => props.revealed ? '#fff' : '#d0d0d0'};
  }
  &:disabled {
    cursor: not-allowed;
  }
`;

const Controls = styled.div`
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Button = styled.button`
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #4CAF50;
  color: white;
  cursor: pointer;
  font-size: 14px;
  &:hover {
    background: #45a049;
  }
`;

const Status = styled.div`
  font-size: 16px;
  font-weight: bold;
  color: #333;
`;

const Minesweeper = () => {
  const [size, setSize] = useState(10);
  const [mines, setMines] = useState(10);
  const [board, setBoard] = useState([]);
  const [gameOver, setGameOver] = useState(false);
  const [win, setWin] = useState(false);
  const [flagsLeft, setFlagsLeft] = useState(10);
  const [revealed, setRevealed] = useState(new Set());
  const [flagged, setFlagged] = useState(new Set());

  // 初始化游戏板
  const initializeBoard = () => {
    const newBoard = Array(size).fill().map(() => Array(size).fill(0));
    let minesPlaced = 0;

    // 随机放置地雷
    while (minesPlaced < mines) {
      const x = Math.floor(Math.random() * size);
      const y = Math.floor(Math.random() * size);
      if (newBoard[x][y] !== -1) {
        newBoard[x][y] = -1;
        minesPlaced++;
      }
    }

    // 计算每个格子周围的地雷数
    for (let i = 0; i < size; i++) {
      for (let j = 0; j < size; j++) {
        if (newBoard[i][j] === -1) continue;
        let count = 0;
        for (let dx = -1; dx <= 1; dx++) {
          for (let dy = -1; dy <= 1; dy++) {
            const ni = i + dx;
            const nj = j + dy;
            if (ni >= 0 && ni < size && nj >= 0 && nj < size && newBoard[ni][nj] === -1) {
              count++;
            }
          }
        }
        newBoard[i][j] = count;
      }
    }

    return newBoard;
  };

  // 重置游戏
  const resetGame = () => {
    setBoard(initializeBoard());
    setGameOver(false);
    setWin(false);
    setFlagsLeft(mines);
    setRevealed(new Set());
    setFlagged(new Set());
  };

  // 检查是否获胜
  const checkWin = () => {
    const totalCells = size * size;
    const revealedCount = revealed.size;
    const minesCount = mines;
    return revealedCount === totalCells - minesCount;
  };

  // 揭示格子
  const revealCell = (x, y) => {
    if (gameOver || win || flagged.has(`${x},${y}`)) return;

    const key = `${x},${y}`;
    if (revealed.has(key)) return;

    const newRevealed = new Set(revealed);
    newRevealed.add(key);

    if (board[x][y] === -1) {
      setGameOver(true);
      return;
    }

    if (board[x][y] === 0) {
      // 如果是空格子，递归揭示周围的格子
      for (let dx = -1; dx <= 1; dx++) {
        for (let dy = -1; dy <= 1; dy++) {
          const ni = x + dx;
          const nj = y + dy;
          if (ni >= 0 && ni < size && nj >= 0 && nj < size) {
            const neighborKey = `${ni},${nj}`;
            if (!newRevealed.has(neighborKey)) {
              revealCell(ni, nj);
            }
          }
        }
      }
    }

    setRevealed(newRevealed);
    if (checkWin()) setWin(true);
  };

  // 标记地雷
  const toggleFlag = (e, x, y) => {
    e.preventDefault();
    if (gameOver || win || revealed.has(`${x},${y}`)) return;

    const key = `${x},${y}`;
    const newFlagged = new Set(flagged);

    if (flagged.has(key)) {
      newFlagged.delete(key);
      setFlagsLeft(flagsLeft + 1);
    } else if (flagsLeft > 0) {
      newFlagged.add(key);
      setFlagsLeft(flagsLeft - 1);
    }

    setFlagged(newFlagged);
  };

  // 初始化游戏
  useEffect(() => {
    resetGame();
  }, []);

  return (
    <GameContainer>
      <Controls>
        <Status>
          {gameOver ? '游戏结束!' : win ? '恭喜获胜!' : `剩余标记: ${flagsLeft}`}
        </Status>
        <Button onClick={resetGame}>重新开始</Button>
      </Controls>
      <GameBoard size={size}>
        {board.map((row, x) =>
          row.map((cell, y) => {
            const key = `${x},${y}`;
            const isRevealed = revealed.has(key);
            const isFlagged = flagged.has(key);
            return (
              <Cell
                key={key}
                revealed={isRevealed}
                onClick={() => revealCell(x, y)}
                onContextMenu={(e) => toggleFlag(e, x, y)}
                disabled={gameOver || win}
              >
                {isRevealed
                  ? cell === -1
                    ? '💣'
                    : cell || ''
                  : isFlagged
                    ? '🚩'
                    : ''}
              </Cell>
            );
          })
        )}
      </GameBoard>
    </GameContainer>
  );
};

export default Minesweeper;