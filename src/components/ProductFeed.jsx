import React, { useState } from 'react';
import styled from 'styled-components';
import products from '../data/products';
import ProductCard from './ProductCard';

const FeedWrapper = styled.div`
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  background: #111;
`;

const NavButton = styled.button`
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  background: rgba(0,0,0,0.5);
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  font-size: 2rem;
  cursor: pointer;
  top: ${props => (props.up ? '32px' : 'unset')};
  bottom: ${props => (props.down ? '32px' : 'unset')};
  opacity: 0.8;
  &:hover { opacity: 1; }
`;

const ProductFeed = () => {
  const [current, setCurrent] = useState(0);

  const goPrev = () => setCurrent(c => (c > 0 ? c - 1 : c));
  const goNext = () => setCurrent(c => (c < products.length - 1 ? c + 1 : c));

  const product = products[current];

  return (
    <FeedWrapper>
      {current > 0 && (
        <NavButton up onClick={goPrev} aria-label="上一个">▲</NavButton>
      )}
      {current < products.length - 1 && (
        <NavButton down onClick={goNext} aria-label="下一个">▼</NavButton>
      )}
      <ProductCard {...product} />
    </FeedWrapper>
  );
};

export default ProductFeed; 